package credit_calculators

import (
	"crypto/tls"
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/httputil"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	XMLPathMerchantID                = "stenik_leasingjetcredit/service/merchant_id"
	XMLPathProductionURL             = "stenik_leasingjetcredit/service/production_url"
	XMLPathProductionCertificatePath = "stenik_leasingjetcredit/service/production_certificate_path"
	XMLPathProductionKeyPath         = "stenik_leasingjetcredit/service/production_key_path"
	XMLPathProductionKeyPassword     = "stenik_leasingjetcredit/service/production_key_password"
	XMLPathSandboxMode               = "stenik_leasingjetcredit/service/sandbox_mode"
	XMLPathTestURL                   = "stenik_leasingjetcredit/service/test_url"
	XMLPathTestCertificatePath       = "stenik_leasingjetcredit/service/test_certificate_path"
	XMLPathTestKeyPath               = "stenik_leasingjetcredit/service/test_key_path"
	XMLPathTestKeyPassword           = "stenik_leasingjetcredit/service/test_key_password"
	XMLPathHideDownPayment           = "payment/stenik_leasingjetcredit/hide_down_payment"
)

// Cache for good categories and types
var (
	goodCategoriesCache     []*model.BNPGoodCategory
	goodCategoriesCacheTime time.Time
	goodCategoriesMutex     sync.RWMutex

	goodTypesCache     map[string][]*model.BNPGoodType
	goodTypesCacheTime map[string]time.Time
	goodTypesMutex     sync.RWMutex

	cacheDuration = 1 * time.Hour // Cache for 1 hour
)

func init() {
	goodTypesCache = make(map[string][]*model.BNPGoodType)
	goodTypesCacheTime = make(map[string]time.Time)
}

type SchemaResponse struct {
	Id   string `json:"pricing_scheme_id"`
	Name string `json:"pricing_scheme_name"`
}

type BNPCalculatorAPI struct {
	merchantID string
	baseURL    string
	client     *http.Client
}

// logHTTPRequest logs detailed information about outgoing HTTP requests
func (b *BNPCalculatorAPI) logHTTPRequest(req *http.Request, method string) {
	log.Printf("[INFO] BNP: === OUTGOING REQUEST ===")
	log.Printf("[INFO] BNP: Method: %s", method)
	log.Printf("[INFO] BNP: Timestamp: %s", time.Now().Format(time.RFC3339))
	log.Printf("[INFO] BNP: URL: %s", req.URL.String())
	log.Printf("[INFO] BNP: HTTP Method: %s", req.Method)

	// Log headers (excluding sensitive data)
	log.Printf("[DEBUG] BNP: Request Headers:")
	for name, values := range req.Header {
		for _, value := range values {
			log.Printf("[DEBUG] BNP:   %s: %s", name, value)
		}
	}

	// Log request parameters extracted from URL
	if req.URL.Path != "" {
		pathParts := strings.Split(strings.Trim(req.URL.Path, "/"), "/")
		if len(pathParts) >= 2 {
			log.Printf("[DEBUG] BNP: Request Parameters:")

			// Log path parts safely based on actual length
			for i, part := range pathParts {
				switch i {
				case 0:
					log.Printf("[DEBUG] BNP:   Service Path: %s", part)
				case 1:
					log.Printf("[DEBUG] BNP:   API Method: %s", part)
				case 2:
					log.Printf("[DEBUG] BNP:   Merchant ID: %s", part)
				case 3:
					log.Printf("[DEBUG] BNP:   Good Type IDs: %s", part)
				case 4:
					log.Printf("[DEBUG] BNP:   Principal: %s", part)
				case 5:
					log.Printf("[DEBUG] BNP:   Down Payment: %s", part)
				default:
					log.Printf("[DEBUG] BNP:   Additional Param[%d]: %s", i, part)
				}
			}
		}
	}

	// Log full request dump for debugging
	if reqDump, err := httputil.DumpRequestOut(req, false); err == nil {
		log.Printf("[DEBUG] BNP: Full Request Dump:\n%s", string(reqDump))
	}
}

// logHTTPResponse logs detailed information about incoming HTTP responses
func (b *BNPCalculatorAPI) logHTTPResponse(resp *http.Response, responseBody []byte, duration time.Duration, method string) {
	log.Printf("[INFO] BNP: === INCOMING RESPONSE ===")
	log.Printf("[INFO] BNP: Method: %s", method)
	log.Printf("[INFO] BNP: Timestamp: %s", time.Now().Format(time.RFC3339))
	log.Printf("[INFO] BNP: Duration: %v", duration)
	log.Printf("[INFO] BNP: Status Code: %d", resp.StatusCode)
	log.Printf("[INFO] BNP: Status: %s", resp.Status)

	// Log response headers
	log.Printf("[DEBUG] BNP: Response Headers:")
	for name, values := range resp.Header {
		for _, value := range values {
			log.Printf("[DEBUG] BNP:   %s: %s", name, value)
		}
	}

	// Log response body length
	log.Printf("[DEBUG] BNP: Response Body Length: %d bytes", len(responseBody))

	// Log response body content
	if len(responseBody) > 0 {
		log.Printf("[DEBUG] BNP: Response Body Content:\n%s", string(responseBody))
	} else {
		log.Printf("[DEBUG] BNP: Response Body: <empty>")
	}

	// Log response analysis
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		log.Printf("[INFO] BNP: Response Status: SUCCESS")
	} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
		log.Printf("[WARN] BNP: Response Status: CLIENT ERROR")
	} else if resp.StatusCode >= 500 {
		log.Printf("[ERROR] BNP: Response Status: SERVER ERROR")
	} else {
		log.Printf("[INFO] BNP: Response Status: INFORMATIONAL/REDIRECT")
	}
}

// logAPIError logs detailed error information
func (b *BNPCalculatorAPI) logAPIError(err error, method string, context string) {
	log.Printf("[ERROR] BNP: === API ERROR ===")
	log.Printf("[ERROR] BNP: Method: %s", method)
	log.Printf("[ERROR] BNP: Context: %s", context)
	log.Printf("[ERROR] BNP: Timestamp: %s", time.Now().Format(time.RFC3339))
	log.Printf("[ERROR] BNP: Error Type: %T", err)
	log.Printf("[ERROR] BNP: Error Message: %v", err)

	// Log additional error context based on error type
	if strings.Contains(err.Error(), "timeout") {
		log.Printf("[ERROR] BNP: Error Category: TIMEOUT")
		log.Printf("[ERROR] BNP: Recommendation: Check network connectivity and BNP API availability")
	} else if strings.Contains(err.Error(), "connection") {
		log.Printf("[ERROR] BNP: Error Category: CONNECTION")
		log.Printf("[ERROR] BNP: Recommendation: Verify BNP API endpoint and network configuration")
	} else if strings.Contains(err.Error(), "certificate") || strings.Contains(err.Error(), "tls") {
		log.Printf("[ERROR] BNP: Error Category: TLS/CERTIFICATE")
		log.Printf("[ERROR] BNP: Recommendation: Check certificate validity and TLS configuration")
	} else if strings.Contains(err.Error(), "xml") || strings.Contains(err.Error(), "unmarshal") {
		log.Printf("[ERROR] BNP: Error Category: RESPONSE_PARSING")
		log.Printf("[ERROR] BNP: Recommendation: Check BNP API response format")
	} else {
		log.Printf("[ERROR] BNP: Error Category: UNKNOWN")
	}
}

// logBusinessLogic logs business logic operations
func (b *BNPCalculatorAPI) logBusinessLogic(operation string, details map[string]interface{}) {
	log.Printf("[INFO] BNP: === BUSINESS LOGIC ===")
	log.Printf("[INFO] BNP: Operation: %s", operation)
	log.Printf("[INFO] BNP: Timestamp: %s", time.Now().Format(time.RFC3339))

	for key, value := range details {
		log.Printf("[DEBUG] BNP: %s: %v", key, value)
	}
}

// handleBNPAPIError provides specific error handling for documented BNP error codes
func handleBNPAPIError(errorCode, errorMessage, methodName string) error {
	switch errorCode {
	case "403":
		return fmt.Errorf("BNP API authentication failed - unrecognized certificate")
	case "404":
		return fmt.Errorf("BNP API endpoint not found - invalid URL")
	case "500":
		return fmt.Errorf("BNP API server error during request processing")
	case "501":
		return fmt.Errorf("BNP API error loading good categories")
	case "5011":
		return fmt.Errorf("BNP API invalid input parameters for GetGoodCategories")
	case "502":
		return fmt.Errorf("BNP API error loading good types")
	case "5021":
		return fmt.Errorf("BNP API invalid input parameters for GetGoodTypes")
	case "503":
		return fmt.Errorf("BNP API error loading pricing schemes")
	case "5031":
		return fmt.Errorf("BNP API invalid down payment amount: %s", errorMessage)
	case "5032":
		return fmt.Errorf("BNP API invalid input parameters for GetAvailablePricingSchemes")
	case "504":
		return fmt.Errorf("BNP API error loading pricing variants")
	case "5041":
		return fmt.Errorf("BNP API invalid down payment amount: %s", errorMessage)
	case "5042":
		return fmt.Errorf("BNP API invalid input parameters for GetAvailablePricingVariants")
	case "505":
		return fmt.Errorf("BNP API error calculating loan parameters")
	case "5051":
		return fmt.Errorf("BNP API invalid down payment amount: %s", errorMessage)
	case "5052":
		return fmt.Errorf("BNP API invalid input parameters for CalculateLoan")
	case "506":
		return fmt.Errorf("BNP API error processing request")
	case "601":
		return fmt.Errorf("BNP API error loading good categories")
	case "602":
		return fmt.Errorf("BNP API error loading good types")
	case "603":
		return fmt.Errorf("BNP API error loading pricing schemes")
	case "6031":
		return fmt.Errorf("BNP API merchant not found")
	case "6032":
		return fmt.Errorf("BNP API product not found")
	case "604":
		return fmt.Errorf("BNP API error loading pricing variants")
	case "605":
		return fmt.Errorf("BNP API error calculating loan parameters")
	case "606":
		return fmt.Errorf("BNP API error processing request")
	default:
		return fmt.Errorf("BNP API error [%s]: %s", errorCode, errorMessage)
	}
}

// validateBNPParameters validates input parameters according to BNP business rules
func validateBNPParameters(principal, downPayment float64, methodName string) error {
	if principal < 0 {
		return fmt.Errorf("principal amount cannot be negative: %.2f", principal)
	}

	if downPayment < 0 {
		return fmt.Errorf("down payment cannot be negative: %.2f", downPayment)
	}

	// Additional validation based on BNP business rules
	if downPayment > principal {
		return fmt.Errorf("down payment (%.2f) cannot exceed principal amount (%.2f)", downPayment, principal)
	}

	// Minimum amounts validation (these may need to be configured)
	if principal < 100 {
		return fmt.Errorf("principal amount too low: minimum 100.00 BGN required")
	}

	return nil
}

func GetAvailableVariantsGroupedByPricingScheme(
	goodTypeIds string,
	principal float64,
	downPayment float64,
) ([]*model.BNPVariantGroup, error) {
	log.Printf("[INFO] BNP: Starting GetAvailableVariantsGroupedByPricingScheme with goodTypeIds='%s', principal=%.2f, downPayment=%.2f", goodTypeIds, principal, downPayment)

	var result []*model.BNPVariantGroup
	if goodTypeIds == "" {
		log.Printf("[WARNING] BNP: goodTypeIds is empty, returning empty result")
		return result, nil
	}

	bnpAPI, err := NewBNPCalculatorAPI()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to create BNP API client: %v", err)
		return result, err
	}

	schemas, err := bnpAPI.GetAvailablePricingSchemes(goodTypeIds, principal, downPayment)
	if err != nil {
		return result, err
	}

	var schemasGroups = make(map[string]*model.BNPVariantGroup)
	for _, schema := range schemas {
		log.Printf("[DEBUG] BNP: === PROCESSING SCHEMA ===")
		log.Printf("[DEBUG] BNP: Schema ID: %s", schema.Id)
		log.Printf("[DEBUG] BNP: Schema Name: %s", schema.Name)

		var data model.BNPVariantGroup
		data, err = bnpAPI.GetAvailablePricingVariants(
			goodTypeIds, principal, downPayment, 0, schema.Id)
		if err == nil {
			log.Printf("[DEBUG] BNP: Successfully fetched %d variants for schema %s", len(data.Variants), schema.Id)

			// Log each variant's APR for this schema
			for i, variant := range data.Variants {
				log.Printf("[DEBUG] BNP: Schema %s - Variant %d: ID=%s, APR=%s, SchemeID=%s",
					schema.Id, i, variant.ID, variant.Apr, variant.PricingSchemeID)
			}

			if v, ok := schemasGroups[schema.Id]; !ok {
				schemasGroups[schema.Id] = &model.BNPVariantGroup{
					SchemeID: schema.Id,
					Variants: data.Variants,
				}
				log.Printf("[DEBUG] BNP: Created new group for schema %s with %d variants", schema.Id, len(data.Variants))
			} else {
				v.Variants = append(v.Variants, data.Variants...)
				log.Printf("[DEBUG] BNP: Appended %d variants to existing group for schema %s (total: %d)",
					len(data.Variants), schema.Id, len(v.Variants))
			}
		} else {
			log.Printf("[ERROR] BNP: Failed to fetch variants for schema %s: %v", schema.Id, err)
		}
		log.Printf("[DEBUG] BNP: === END PROCESSING SCHEMA %s ===", schema.Id)
	}

	var resultList []*model.BNPVariantGroup
	log.Printf("[DEBUG] BNP: === FINAL RESULT ASSEMBLY ===")
	for schemeId, item := range schemasGroups {
		log.Printf("[DEBUG] BNP: Final group - Schema ID: %s, Variants count: %d", schemeId, len(item.Variants))
		for i, variant := range item.Variants {
			log.Printf("[DEBUG] BNP: Final - Schema %s, Variant %d: ID=%s, APR=%s, SchemeID=%s",
				schemeId, i, variant.ID, variant.Apr, variant.PricingSchemeID)
		}
		resultList = append(resultList, item)
	}
	log.Printf("[DEBUG] BNP: === END FINAL RESULT ASSEMBLY ===")
	log.Printf("[INFO] BNP: Returning %d variant groups", len(resultList))

	return resultList, nil
}

func NewBNPCalculatorAPI() (*BNPCalculatorAPI, error) {
	log.Printf("[DEBUG] BNP: Starting BNP Calculator API initialization")

	merchantID := getMerchantID()
	if merchantID == "" {
		log.Printf("[ERROR] BNP: Merchant ID is not configured")
		return nil, fmt.Errorf("BNP merchant ID is not configured")
	}
	log.Printf("[INFO] BNP: Using merchant ID: %s", merchantID)

	baseURL := getBaseUrl()
	if baseURL == "" {
		log.Printf("[ERROR] BNP: Base URL is not configured")
		return nil, fmt.Errorf("BNP base URL is not configured")
	}
	log.Printf("[INFO] BNP: Using base URL: %s", baseURL)

	sandboxMode := isSandboxMode()
	log.Printf("[INFO] BNP: Operating in mode: %s", func() string {
		if sandboxMode {
			return "SANDBOX"
		}
		return "PRODUCTION"
	}())

	tslConfig, err := getTSLConfig()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to get TLS configuration: %v", err)
		return nil, err
	}
	log.Printf("[INFO] BNP: TLS configuration loaded successfully")

	return &BNPCalculatorAPI{
		merchantID: merchantID,
		baseURL:    strings.TrimLeft(baseURL, "/"),
		client: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: tslConfig,
			},
			Timeout: 30 * time.Second,
		},
	}, nil
}

type SchemaRow struct {
	PricingSchemeId   string `xml:"PricingSchemeId"`
	PricingSchemeName string `xml:"PricingSchemeName"`
}

type SchemaData struct {
	XMLName      xml.Name `xml:"Result"`
	ErrorCode    string   `xml:"ErrorCode"`
	ErrorMessage string   `xml:"ErrorMessage"`
	Data         struct {
		PricingScheme []SchemaRow `xml:"PricingScheme"`
	} `xml:"Data"`
}

func (b *BNPCalculatorAPI) GetAvailablePricingSchemes(
	goodTypeIds string,
	principal float64,
	downPayment float64,
) ([]SchemaResponse, error) {
	methodName := "GetAvailablePricingSchemes"
	startTime := time.Now()

	// Log business logic - parameter validation
	b.logBusinessLogic("Parameter Validation", map[string]interface{}{
		"method":       methodName,
		"goodTypeIds":  goodTypeIds,
		"principal":    principal,
		"downPayment": downPayment,
	})

	var result []SchemaResponse
	if goodTypeIds == "" {
		log.Printf("[WARN] BNP: %s called with empty goodTypeIds, returning empty result", methodName)
		return result, nil
	}

	// Validate parameters using enhanced validation
	err := validateBNPParameters(principal, downPayment, methodName)
	if err != nil {
		b.logAPIError(err, methodName, "Parameter Validation")
		return nil, err
	}

	// Create HTTP request with correct parameter order per BNP documentation:
	// PosId/Goods/TotalGoodPrice/DownPayment
	req, err := http.NewRequest("GET", b.getMethodURL(methodName, []string{
		b.merchantID,    // PosId
		goodTypeIds,     // Goods
		fmt.Sprintf("%.2f", principal), // TotalGoodPrice
		fmt.Sprintf("%.2f", downPayment), // DownPayment
	}), nil)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Creation")
		return nil, err
	}

	req.Header.Set("User-Agent", "MerchantPos")

	// Log outgoing request
	b.logHTTPRequest(req, methodName)

	// Execute HTTP request
	resp, err := b.client.Do(req)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Execution")
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	responseContent, err := io.ReadAll(resp.Body)
	if err != nil {
		b.logAPIError(err, methodName, "Response Body Reading")
		return nil, err
	}

	// Log incoming response
	duration := time.Since(startTime)
	b.logHTTPResponse(resp, responseContent, duration, methodName)

	// Parse XML response
	var schemaData SchemaData
	err = xml.Unmarshal(responseContent, &schemaData)
	if err != nil {
		b.logAPIError(fmt.Errorf("XML parsing failed: %w", err), methodName, "Response Parsing")
		return nil, fmt.Errorf("invalid response format: %w", err)
	}

	// Log business logic - response validation
	b.logBusinessLogic("Response Validation", map[string]interface{}{
		"errorCode":    schemaData.ErrorCode,
		"errorMessage": schemaData.ErrorMessage,
		"schemeCount":  len(schemaData.Data.PricingScheme),
	})

	// Check for API errors using enhanced error handling
	if schemaData.ErrorCode != "" && schemaData.ErrorCode != "0" {
		err := handleBNPAPIError(schemaData.ErrorCode, schemaData.ErrorMessage, methodName)
		b.logAPIError(err, methodName, "BNP API Response Error")
		return nil, err
	}

	// Transform response data
	if len(schemaData.Data.PricingScheme) > 0 {
		for _, item := range schemaData.Data.PricingScheme {
			result = append(result, SchemaResponse{
				Id:   item.PricingSchemeId,
				Name: item.PricingSchemeName,
			})
		}

		// Log successful transformation
		b.logBusinessLogic("Data Transformation", map[string]interface{}{
			"inputSchemes":  len(schemaData.Data.PricingScheme),
			"outputSchemes": len(result),
			"success":       true,
		})
	} else {
		log.Printf("[WARN] BNP: %s returned no pricing schemes", methodName)
	}

	log.Printf("[INFO] BNP: %s completed successfully in %v, returned %d schemes", methodName, duration, len(result))
	return result, nil
}

type VariantRow struct {
	PricingVariantId         string `xml:"PricingVariantId"`
	APR                      string `xml:"APR"`
	CorrectDownPaymentAmount string `xml:"CorrectDownPaymentAmount"`
	InstallmentAmount        string `xml:"InstallmentAmount"`
	Maturity                 string `xml:"Maturity"`
	NIR                      string `xml:"NIR"`
	PricingSchemeId          string `xml:"PricingSchemeId"`
	PricingSchemeName        string `xml:"PricingSchemeName"`
	ProcessingFeeAmount      string `xml:"ProcessingFeeAmount"`
	TotalRepaymentAmount     string `xml:"TotalRepaymentAmount"`
}

type PricingVariantsData struct {
	XMLName      xml.Name `xml:"Result"`
	ErrorCode    string   `xml:"ErrorCode"`
	ErrorMessage string   `xml:"ErrorMessage"`
	Data         struct {
		PricingVariant []VariantRow `xml:"PricingVariant"`
	} `xml:"Data"`
}

func (b *BNPCalculatorAPI) GetAvailablePricingVariants(
	goodTypeIds string,
	principal float64,
	downPayment float64,
	instalment float64,
	schemeId string,
) (model.BNPVariantGroup, error) {
	methodName := "GetAvailablePricingVariants"
	startTime := time.Now()

	// Log business logic - parameter validation
	b.logBusinessLogic("Parameter Validation", map[string]interface{}{
		"method":       methodName,
		"goodTypeIds":  goodTypeIds,
		"principal":    principal,
		"downPayment": downPayment,
		"instalment":   instalment,
		"schemeId":     schemeId,
	})

	result := model.BNPVariantGroup{
		SchemeID: schemeId,
		Variants: []*model.PricingVariant{},
	}

	if goodTypeIds == "" {
		log.Printf("[WARN] BNP: %s called with empty goodTypeIds, returning empty result", methodName)
		return result, nil
	}

	// Validate parameters using enhanced validation
	err := validateBNPParameters(principal, downPayment, methodName)
	if err != nil {
		b.logAPIError(err, methodName, "Parameter Validation")
		return result, err
	}

	if instalment < 0 {
		err := fmt.Errorf("instalment cannot be negative: %.2f", instalment)
		b.logAPIError(err, methodName, "Parameter Validation")
		return result, err
	}

	// Create HTTP request with correct parameter order per BNP documentation:
	// PosId/Goods/TotalGoodsPrice/DownPayment/PreferredInstallment/PricingSchemeId
	req, err := http.NewRequest("GET", b.getMethodURL(methodName, []string{
		b.merchantID,    // PosId
		goodTypeIds,     // Goods
		fmt.Sprintf("%.2f", principal), // TotalGoodsPrice
		fmt.Sprintf("%.2f", downPayment), // DownPayment
		fmt.Sprintf("%.2f", instalment), // PreferredInstallment
		schemeId,        // PricingSchemeId
	}), nil)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Creation")
		return result, err
	}

	req.Header.Set("User-Agent", "MerchantPos")

	// Log outgoing request
	b.logHTTPRequest(req, methodName)

	// Execute HTTP request
	resp, err := b.client.Do(req)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Execution")
		return result, err
	}
	defer resp.Body.Close()

	// Read response body
	responseContent, err := io.ReadAll(resp.Body)
	if err != nil {
		b.logAPIError(err, methodName, "Response Body Reading")
		return result, err
	}

	// Log incoming response
	duration := time.Since(startTime)
	b.logHTTPResponse(resp, responseContent, duration, methodName)

	// Parse XML response
	var schemaData PricingVariantsData
	err = xml.Unmarshal(responseContent, &schemaData)
	if err != nil {
		b.logAPIError(fmt.Errorf("XML parsing failed: %w", err), methodName, "Response Parsing")
		return result, fmt.Errorf("invalid response format: %w", err)
	}

	// Log business logic - response validation
	b.logBusinessLogic("Response Validation", map[string]interface{}{
		"errorCode":     schemaData.ErrorCode,
		"errorMessage":  schemaData.ErrorMessage,
		"variantCount":  len(schemaData.Data.PricingVariant),
	})

	// Check for API errors using enhanced error handling
	if schemaData.ErrorCode != "" && schemaData.ErrorCode != "0" {
		err := handleBNPAPIError(schemaData.ErrorCode, schemaData.ErrorMessage, methodName)
		b.logAPIError(err, methodName, "BNP API Response Error")
		return result, err
	}

	// Transform response data
	if len(schemaData.Data.PricingVariant) > 0 {
		for _, item := range schemaData.Data.PricingVariant {
			// Handle empty ProcessingFeeAmount by defaulting to "0"
			processingFeeAmount := item.ProcessingFeeAmount
			if processingFeeAmount == "" {
				processingFeeAmount = "0"
				log.Printf("[DEBUG] BNP: ProcessingFeeAmount was empty for variant %s, defaulting to '0'", item.PricingVariantId)
			}

			// Clean up illogical negative APR values for promotional schemes
			cleanAPR := item.APR
			if aprFloat, err := strconv.ParseFloat(item.APR, 64); err == nil && aprFloat < 0 {
				log.Printf("[INFO] BNP: Normalizing negative APR %s%% to 0.00%% for variant %s (promotional scheme)", item.APR, item.PricingVariantId)
				cleanAPR = "0.00"
			}

			// DEBUGGING: Log detailed APR processing for each variant
			log.Printf("[DEBUG] BNP: === VARIANT APR PROCESSING ===")
			log.Printf("[DEBUG] BNP: Scheme ID: %s", schemeId)
			log.Printf("[DEBUG] BNP: Variant ID: %s", item.PricingVariantId)
			log.Printf("[DEBUG] BNP: Original APR from API: '%s'", item.APR)
			log.Printf("[DEBUG] BNP: Clean APR after processing: '%s'", cleanAPR)
			log.Printf("[DEBUG] BNP: Expected Scheme ID in variant: '%s'", item.PricingSchemeId)
			log.Printf("[DEBUG] BNP: === END VARIANT APR PROCESSING ===")

			variant := &model.PricingVariant{
				ID:                       item.PricingVariantId,
				Apr:                      cleanAPR,
				CorrectDownpaymentAmount: item.CorrectDownPaymentAmount,
				InstallmentAmount:        item.InstallmentAmount,
				Maturity:                 item.Maturity,
				Nir:                      item.NIR,
				PricingSchemeID:          item.PricingSchemeId,
				PricingSchemeName:        item.PricingSchemeName,
				ProcessingFeeAmount:      processingFeeAmount,
				TotalRepaymentAmount:     item.TotalRepaymentAmount,
				// Legacy field names for Magento template compatibility
				Installment:              item.InstallmentAmount,
				TotalRepayment:           item.TotalRepaymentAmount,
			}
			result.Variants = append(result.Variants, variant)

			// Log each variant details with enhanced debugging
			log.Printf("[DEBUG] BNP: Parsed variant ID=%s, APR=%s->%s, Scheme=%s, Maturity=%s, InstallmentAmount=%s",
				item.PricingVariantId, item.APR, cleanAPR, item.PricingSchemeId, item.Maturity, item.InstallmentAmount)
		}

		// Log successful transformation
		b.logBusinessLogic("Data Transformation", map[string]interface{}{
			"inputVariants":  len(schemaData.Data.PricingVariant),
			"outputVariants": len(result.Variants),
			"schemeId":       schemeId,
			"success":        true,
		})
	} else {
		log.Printf("[WARN] BNP: %s returned no pricing variants for scheme %s", methodName, schemeId)
	}

	log.Printf("[INFO] BNP: %s completed successfully in %v, returned %d variants for scheme %s",
		methodName, duration, len(result.Variants), schemeId)
	return result, nil
}

type LoanCalculationData struct {
	XMLName      xml.Name `xml:"Result"`
	ErrorCode    string   `xml:"ErrorCode"`
	ErrorMessage string   `xml:"ErrorMessage"`
	Data         struct {
		CreditProposition LoanCalculationResult `xml:"CreditProposition"`
	} `xml:"Data"`
}

type LoanCalculationResult struct {
	APR                      string `xml:"APR"`
	CorrectDownPaymentAmount string `xml:"CorrectDownPaymentAmount"`
	InstallmentAmount        string `xml:"InstallmentAmount"`
	Maturity                 string `xml:"Maturity"`
	NIR                      string `xml:"NIR"`
	PricingSchemeId          string `xml:"PricingSchemeId"`
	PricingSchemeName        string `xml:"PricingSchemeName"`
	PricingVariantId         string `xml:"PricingVariantId"`
	ProcessingFeeAmount      string `xml:"ProcessingFeeAmount"`
	TotalRepaymentAmount     string `xml:"TotalRepaymentAmount"`
}

func (b *BNPCalculatorAPI) CalculateLoan(
	goodTypeIds string,
	principal float64,
	downPayment float64,
	pricingVariantId int,
) (*model.LoanCalculation, error) {
	methodName := "CalculateLoan"
	startTime := time.Now()

	// Log business logic - parameter validation
	b.logBusinessLogic("Parameter Validation", map[string]interface{}{
		"method":           methodName,
		"goodTypeIds":      goodTypeIds,
		"principal":        principal,
		"downPayment":     downPayment,
		"pricingVariantId": pricingVariantId,
	})

	if goodTypeIds == "" {
		err := fmt.Errorf("goodTypeIds cannot be empty")
		b.logAPIError(err, methodName, "Parameter Validation")
		return nil, err
	}

	// Validate parameters using enhanced validation
	err := validateBNPParameters(principal, downPayment, methodName)
	if err != nil {
		b.logAPIError(err, methodName, "Parameter Validation")
		return nil, err
	}

	// Additional validation for CalculateLoan - principal must be positive
	if principal <= 0 {
		err := fmt.Errorf("principal must be greater than 0: %.2f", principal)
		b.logAPIError(err, methodName, "Parameter Validation")
		return nil, err
	}

	if pricingVariantId <= 0 {
		err := fmt.Errorf("pricingVariantId must be greater than 0: %d", pricingVariantId)
		b.logAPIError(err, methodName, "Parameter Validation")
		return nil, err
	}

	// Create HTTP request
	req, err := http.NewRequest("GET", b.getMethodURL(methodName, []string{
		b.merchantID,
		goodTypeIds,
		fmt.Sprintf("%.2f", principal),
		fmt.Sprintf("%.2f", downPayment),
		fmt.Sprintf("%d", pricingVariantId),
	}), nil)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Creation")
		return nil, err
	}

	req.Header.Set("User-Agent", "MerchantPos")

	// Log outgoing request
	b.logHTTPRequest(req, methodName)

	// Execute HTTP request
	resp, err := b.client.Do(req)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Execution")
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	responseContent, err := io.ReadAll(resp.Body)
	if err != nil {
		b.logAPIError(err, methodName, "Response Body Reading")
		return nil, err
	}

	// Log incoming response
	duration := time.Since(startTime)
	b.logHTTPResponse(resp, responseContent, duration, methodName)

	// Parse XML response
	var loanData LoanCalculationData
	err = xml.Unmarshal(responseContent, &loanData)
	if err != nil {
		b.logAPIError(fmt.Errorf("XML parsing failed: %w", err), methodName, "Response Parsing")
		return nil, fmt.Errorf("invalid response format: %w", err)
	}

	// Log business logic - response validation
	b.logBusinessLogic("Response Validation", map[string]interface{}{
		"errorCode":    loanData.ErrorCode,
		"errorMessage": loanData.ErrorMessage,
		"hasLoanData":  loanData.Data.CreditProposition.APR != "",
	})

	// Check for API errors using enhanced error handling
	if loanData.ErrorCode != "" && loanData.ErrorCode != "0" {
		err := handleBNPAPIError(loanData.ErrorCode, loanData.ErrorMessage, methodName)
		b.logAPIError(err, methodName, "BNP API Response Error")
		return nil, err
	}

	// Transform response data
	// Handle empty ProcessingFeeAmount by defaulting to "0"
	processingFeeAmount := loanData.Data.CreditProposition.ProcessingFeeAmount
	if processingFeeAmount == "" {
		processingFeeAmount = "0"
		log.Printf("[DEBUG] BNP: ProcessingFeeAmount was empty for loan calculation, defaulting to '0'")
	}

	result := &model.LoanCalculation{
		Apr:                      loanData.Data.CreditProposition.APR,
		CorrectDownpaymentAmount: loanData.Data.CreditProposition.CorrectDownPaymentAmount,
		InstallmentAmount:        loanData.Data.CreditProposition.InstallmentAmount,
		Maturity:                 loanData.Data.CreditProposition.Maturity,
		Nir:                      loanData.Data.CreditProposition.NIR,
		PricingSchemeID:          loanData.Data.CreditProposition.PricingSchemeId,
		PricingSchemeName:        loanData.Data.CreditProposition.PricingSchemeName,
		PricingVariantID:         loanData.Data.CreditProposition.PricingVariantId,
		ProcessingFeeAmount:      processingFeeAmount,
		TotalRepaymentAmount:     loanData.Data.CreditProposition.TotalRepaymentAmount,
	}

	// Log successful transformation with loan details
	b.logBusinessLogic("Data Transformation", map[string]interface{}{
		"apr":                   result.Apr,
		"installmentAmount":     result.InstallmentAmount,
		"maturity":             result.Maturity,
		"totalRepaymentAmount": result.TotalRepaymentAmount,
		"processingFeeAmount":  result.ProcessingFeeAmount,
		"success":              true,
	})

	log.Printf("[INFO] BNP: %s completed successfully in %v for variant %d", methodName, duration, pricingVariantId)
	log.Printf("[INFO] BNP: Loan Details - APR: %s, Installment: %s, Maturity: %s months, Total: %s",
		result.Apr, result.InstallmentAmount, result.Maturity, result.TotalRepaymentAmount)

	return result, nil
}

// Good Category and Type structures
type GoodCategoryRow struct {
	GoodCategoryId   string `xml:"GoodCategoryId"`
	GoodCategoryName string `xml:"GoodCategoryName"`
}

type GoodCategoriesData struct {
	XMLName      xml.Name `xml:"Result"`
	ErrorCode    string   `xml:"ErrorCode"`
	ErrorMessage string   `xml:"ErrorMessage"`
	Data         struct {
		GoodCategory []GoodCategoryRow `xml:"GoodCategory"`
	} `xml:"Data"`
}

type GoodTypeRow struct {
	GoodTypeId   string `xml:"GoodTypeId"`
	GoodTypeName string `xml:"GoodTypeName"`
}

type GoodTypesData struct {
	XMLName      xml.Name `xml:"Result"`
	ErrorCode    string   `xml:"ErrorCode"`
	ErrorMessage string   `xml:"ErrorMessage"`
	Data         struct {
		GoodType []GoodTypeRow `xml:"GoodType"`
	} `xml:"Data"`
}

// GetGoodCategories retrieves all available good categories from BNP API
func (b *BNPCalculatorAPI) GetGoodCategories() ([]*model.BNPGoodCategory, error) {
	methodName := "GetGoodCategories"
	startTime := time.Now()

	log.Printf("[INFO] BNP: Starting %s request", methodName)

	// Log business logic - parameter validation
	b.logBusinessLogic("Parameter Validation", map[string]interface{}{
		"method":     methodName,
		"merchantId": b.merchantID,
	})

	// Create HTTP request
	req, err := http.NewRequest("GET", b.getMethodURL(methodName, []string{
		b.merchantID,
	}), nil)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Creation")
		return nil, err
	}

	req.Header.Set("User-Agent", "MerchantPos")

	// Log outgoing request
	b.logHTTPRequest(req, methodName)

	// Execute HTTP request
	resp, err := b.client.Do(req)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Execution")
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	responseContent, err := io.ReadAll(resp.Body)
	if err != nil {
		b.logAPIError(err, methodName, "Response Body Reading")
		return nil, err
	}

	// Log incoming response
	duration := time.Since(startTime)
	b.logHTTPResponse(resp, responseContent, duration, methodName)

	// Parse XML response
	var categoriesData GoodCategoriesData
	err = xml.Unmarshal(responseContent, &categoriesData)
	if err != nil {
		b.logAPIError(fmt.Errorf("XML parsing failed: %w", err), methodName, "Response Parsing")
		return nil, fmt.Errorf("invalid response format: %w", err)
	}

	// Log business logic - response validation
	b.logBusinessLogic("Response Validation", map[string]interface{}{
		"errorCode":      categoriesData.ErrorCode,
		"errorMessage":   categoriesData.ErrorMessage,
		"categoryCount":  len(categoriesData.Data.GoodCategory),
	})

	// Check for API errors using enhanced error handling
	if categoriesData.ErrorCode != "" && categoriesData.ErrorCode != "0" {
		err := handleBNPAPIError(categoriesData.ErrorCode, categoriesData.ErrorMessage, methodName)
		b.logAPIError(err, methodName, "BNP API Response Error")
		return nil, err
	}

	// Transform response data
	var result []*model.BNPGoodCategory
	if len(categoriesData.Data.GoodCategory) > 0 {
		for _, item := range categoriesData.Data.GoodCategory {
			// Convert string ID to int64
			categoryID, err := strconv.ParseInt(item.GoodCategoryId, 10, 64)
			if err != nil {
				log.Printf("[WARNING] BNP: Failed to parse category ID '%s': %v", item.GoodCategoryId, err)
				continue
			}

			category := &model.BNPGoodCategory{
				ID:   categoryID,
				Name: item.GoodCategoryName,
			}
			result = append(result, category)

			// Log each category details
			log.Printf("[DEBUG] BNP: Parsed category ID=%s, Name=%s", item.GoodCategoryId, item.GoodCategoryName)
		}

		// Log successful transformation
		b.logBusinessLogic("Data Transformation", map[string]interface{}{
			"inputCategories":  len(categoriesData.Data.GoodCategory),
			"outputCategories": len(result),
			"success":          true,
		})
	} else {
		log.Printf("[WARN] BNP: %s returned no good categories", methodName)
	}

	log.Printf("[INFO] BNP: %s completed successfully in %v, returned %d categories", methodName, duration, len(result))
	return result, nil
}

// GetGoodTypes retrieves good types for a specific category from BNP API
func (b *BNPCalculatorAPI) GetGoodTypes(categoryId string) ([]*model.BNPGoodType, error) {
	methodName := "GetGoodTypes"
	startTime := time.Now()

	log.Printf("[INFO] BNP: Starting %s request for category %s", methodName, categoryId)

	// Log business logic - parameter validation
	b.logBusinessLogic("Parameter Validation", map[string]interface{}{
		"method":     methodName,
		"categoryId": categoryId,
	})

	if categoryId == "" {
		err := fmt.Errorf("categoryId cannot be empty")
		b.logAPIError(err, methodName, "Parameter Validation")
		return nil, err
	}

	// Create HTTP request
	req, err := http.NewRequest("GET", b.getMethodURL(methodName, []string{
		categoryId,
	}), nil)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Creation")
		return nil, err
	}

	req.Header.Set("User-Agent", "MerchantPos")

	// Log outgoing request
	b.logHTTPRequest(req, methodName)

	// Execute HTTP request
	resp, err := b.client.Do(req)
	if err != nil {
		b.logAPIError(err, methodName, "HTTP Request Execution")
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	responseContent, err := io.ReadAll(resp.Body)
	if err != nil {
		b.logAPIError(err, methodName, "Response Body Reading")
		return nil, err
	}

	// Log incoming response
	duration := time.Since(startTime)
	b.logHTTPResponse(resp, responseContent, duration, methodName)

	// Parse XML response
	var typesData GoodTypesData
	err = xml.Unmarshal(responseContent, &typesData)
	if err != nil {
		b.logAPIError(fmt.Errorf("XML parsing failed: %w", err), methodName, "Response Parsing")
		return nil, fmt.Errorf("invalid response format: %w", err)
	}

	// Log business logic - response validation
	b.logBusinessLogic("Response Validation", map[string]interface{}{
		"errorCode":    typesData.ErrorCode,
		"errorMessage": typesData.ErrorMessage,
		"typeCount":    len(typesData.Data.GoodType),
		"categoryId":   categoryId,
	})

	// Check for API errors using enhanced error handling
	if typesData.ErrorCode != "" && typesData.ErrorCode != "0" {
		err := handleBNPAPIError(typesData.ErrorCode, typesData.ErrorMessage, methodName)
		b.logAPIError(err, methodName, "BNP API Response Error")
		return nil, err
	}

	// Transform response data
	var result []*model.BNPGoodType
	if len(typesData.Data.GoodType) > 0 {
		// Convert category ID to int64
		categoryIDInt, err := strconv.ParseInt(categoryId, 10, 64)
		if err != nil {
			log.Printf("[ERROR] BNP: Failed to parse category ID '%s': %v", categoryId, err)
			return nil, fmt.Errorf("invalid category ID: %s", categoryId)
		}

		for _, item := range typesData.Data.GoodType {
			// Convert good type ID to int64
			goodTypeID, err := strconv.ParseInt(item.GoodTypeId, 10, 64)
			if err != nil {
				log.Printf("[WARNING] BNP: Failed to parse good type ID '%s': %v", item.GoodTypeId, err)
				continue
			}

			goodType := &model.BNPGoodType{
				ID:         goodTypeID,
				Name:       item.GoodTypeName,
				CategoryID: categoryIDInt,
			}
			result = append(result, goodType)

			// Log each type details
			log.Printf("[DEBUG] BNP: Parsed good type ID=%s, Name=%s, CategoryID=%s",
				item.GoodTypeId, item.GoodTypeName, categoryId)
		}

		// Log successful transformation
		b.logBusinessLogic("Data Transformation", map[string]interface{}{
			"inputTypes":  len(typesData.Data.GoodType),
			"outputTypes": len(result),
			"categoryId":  categoryId,
			"success":     true,
		})
	} else {
		log.Printf("[WARN] BNP: %s returned no good types for category %s", methodName, categoryId)
	}

	log.Printf("[INFO] BNP: %s completed successfully in %v, returned %d types for category %s",
		methodName, duration, len(result), categoryId)
	return result, nil
}

func (b *BNPCalculatorAPI) getMethodURL(method string, params []string) string {
	// Remove trailing slash from baseURL to avoid double slashes
	baseURL := strings.TrimRight(b.baseURL, "/")
	// Base URL already contains /ServicesPricing/ path, just append method
	url := baseURL + "/" + method

	log.Printf("[DEBUG] BNP: URL Construction - Original Base: %s", b.baseURL)
	log.Printf("[DEBUG] BNP: URL Construction - Trimmed Base: %s, Method: %s", baseURL, method)

	if len(params) > 0 {
		url = url + "/" + strings.Join(params, "/")
		log.Printf("[DEBUG] BNP: URL Parameters: %v", params)
	}

	// Log final URL with emphasis
	log.Printf("[INFO] BNP: Final constructed URL: %s", url)

	return url
}

func getTSLConfig() (*tls.Config, error) {
	log.Printf("[DEBUG] BNP: Starting TLS configuration setup")

	// Get certificate path with detailed logging
	certPath, err := getCertificatePath()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to get certificate path: %v", err)
		return nil, err
	}
	log.Printf("[DEBUG] BNP: Certificate path resolved to: %s", certPath)

	// Get private key path with detailed logging
	keyPath, err := getKeyPath()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to get private key path: %v", err)
		return nil, err
	}
	log.Printf("[DEBUG] BNP: Private key path resolved to: %s", keyPath)

	// Check for key password (not supported)
	keyPassword := getKeyPassword()
	if keyPassword != "" {
		log.Printf("[ERROR] BNP: Key password is configured but not supported")
		return nil, fmt.Errorf("BNP can't use key password")
	}
	log.Printf("[DEBUG] BNP: No key password configured (as expected)")

	// Load certificate and key pair
	log.Printf("[DEBUG] BNP: Loading X509 certificate/key pair")
	cert, err := tls.LoadX509KeyPair(certPath, keyPath)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to load certificate/key pair from paths cert='%s', key='%s': %v", certPath, keyPath, err)
		return nil, fmt.Errorf("failed to load certificate/key pair: %w", err)
	}
	log.Printf("[INFO] BNP: Successfully loaded X509 certificate/key pair")

	// Create TLS configuration - secure production configuration
	tlsConfig := &tls.Config{
		Certificates:       []tls.Certificate{cert},
		InsecureSkipVerify: false, // Secure production configuration
		ServerName:         "ws.pbpf.bg", // Correct server name matching certificate
		MinVersion:         tls.VersionTLS12,  // BNP server uses TLS 1.2
		Renegotiation:      tls.RenegotiateFreelyAsClient,  // Allow renegotiation for client cert
	}

	log.Printf("[INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false")
	log.Printf("[DEBUG] BNP: Certificate count in TLS config: %d", len(tlsConfig.Certificates))

	return tlsConfig, nil
}

func isSandboxMode() bool {
	sandboxMode := praktis.GetPraktisStore().GetConfig(XMLPathSandboxMode, "0")
	log.Printf("[DEBUG] BNP: Sandbox mode config value: '%s' (from path: %s)", sandboxMode, XMLPathSandboxMode)

	issandbox := sandboxMode == "1"
	log.Printf("[DEBUG] BNP: Sandbox mode resolved to: %t", issandbox)

	return issandbox
}

func getMerchantID() string {
	merchantID := praktis.GetPraktisStore().GetConfig(XMLPathMerchantID, "")
	log.Printf("[DEBUG] BNP: Merchant ID from config (path: %s): '%s'", XMLPathMerchantID, merchantID)

	if merchantID == "" {
		log.Printf("[WARNING] BNP: Merchant ID is empty or not configured")
		return ""
	}

	log.Printf("[DEBUG] BNP: Using merchant ID: %s", merchantID)
	return merchantID
}

func getBaseUrl() string {
	var url string
	var configPath string

	if isSandboxMode() {
		configPath = XMLPathTestURL
		url = praktis.GetPraktisStore().GetConfig(XMLPathTestURL, "")
		log.Printf("[DEBUG] BNP: Getting SANDBOX URL from config path: %s", configPath)
	} else {
		configPath = XMLPathProductionURL
		url = praktis.GetPraktisStore().GetConfig(XMLPathProductionURL, "")
		log.Printf("[DEBUG] BNP: Getting PRODUCTION URL from config path: %s", configPath)
	}

	log.Printf("[DEBUG] BNP: Base URL from config: '%s'", url)

	if url == "" {
		log.Printf("[WARNING] BNP: Base URL is empty or not configured in path: %s", configPath)
	}

	return url
}

func getCertificatePath() (string, error) {
	var fileName string
	var configPath string

	if isSandboxMode() {
		configPath = XMLPathTestCertificatePath
		fileName = praktis.GetPraktisStore().GetConfig(XMLPathTestCertificatePath, "")
		log.Printf("[DEBUG] BNP: Getting SANDBOX certificate filename from config path: %s", configPath)
	} else {
		configPath = XMLPathProductionCertificatePath
		fileName = praktis.GetPraktisStore().GetConfig(XMLPathProductionCertificatePath, "")
		log.Printf("[DEBUG] BNP: Getting PRODUCTION certificate filename from config path: %s", configPath)
	}

	log.Printf("[DEBUG] BNP: Raw certificate filename from Magento config: '%s'", fileName)

	if fileName == "" {
		log.Printf("[ERROR] BNP: Certificate filename is empty in configuration path: %s", configPath)
		return "", fmt.Errorf("certificate filename is not configured in path: %s", configPath)
	}

	fullPath, err := praktis.GetCertificateFullPath(fileName)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to resolve certificate full path for filename '%s': %v", fileName, err)
		return "", fmt.Errorf("failed to resolve certificate path for '%s': %w", fileName, err)
	}

	log.Printf("[INFO] BNP: Certificate full path resolved: %s", fullPath)
	return fullPath, nil
}

func getKeyPath() (string, error) {
	var fileName string
	var configPath string

	if isSandboxMode() {
		configPath = XMLPathTestKeyPath
		fileName = praktis.GetPraktisStore().GetConfig(XMLPathTestKeyPath, "")
		log.Printf("[DEBUG] BNP: Getting SANDBOX private key filename from config path: %s", configPath)
	} else {
		configPath = XMLPathProductionKeyPath
		fileName = praktis.GetPraktisStore().GetConfig(XMLPathProductionKeyPath, "")
		log.Printf("[DEBUG] BNP: Getting PRODUCTION private key filename from config path: %s", configPath)
	}

	log.Printf("[DEBUG] BNP: Raw private key filename from Magento config: '%s'", fileName)

	if fileName == "" {
		log.Printf("[ERROR] BNP: Private key filename is empty in configuration path: %s", configPath)
		return "", fmt.Errorf("private key filename is not configured in path: %s", configPath)
	}

	fullPath, err := praktis.GetCertificateFullPath(fileName)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to resolve private key full path for filename '%s': %v", fileName, err)
		return "", fmt.Errorf("failed to resolve private key path for '%s': %w", fileName, err)
	}

	log.Printf("[INFO] BNP: Private key full path resolved: %s", fullPath)
	return fullPath, nil
}

func getKeyPassword() string {
	if isSandboxMode() {
		return praktis.GetPraktisStore().GetConfig(XMLPathTestKeyPassword, "")
	}
	return praktis.GetPraktisStore().GetConfig(XMLPathProductionKeyPassword, "")
}

// GetBNPPricingSchemes returns available pricing schemes for given parameters
func GetBNPPricingSchemes(goodTypeIds string, principal float64, downPayment float64) ([]*model.BNPPricingScheme, error) {
	var result []*model.BNPPricingScheme
	if goodTypeIds == "" {
		return result, nil
	}

	bnpAPI, err := NewBNPCalculatorAPI()
	if err != nil {
		return result, err
	}

	schemas, err := bnpAPI.GetAvailablePricingSchemes(goodTypeIds, principal, downPayment)
	if err != nil {
		return result, err
	}

	for _, schema := range schemas {
		result = append(result, &model.BNPPricingScheme{
			ID:   schema.Id,
			Name: schema.Name,
		})
	}

	return result, nil
}

// CalculateBNPLoan calculates loan details for a specific pricing variant using cart data
func CalculateBNPLoan(cartToken string, downPayment float64, pricingVariantId int) (*model.LoanCalculation, error) {
	log.Printf("[INFO] BNP: Starting CalculateBNPLoan with cartToken='%s', downPayment=%.2f, pricingVariantId=%d", cartToken, downPayment, pricingVariantId)

	if cartToken == "" {
		return nil, fmt.Errorf("cartToken cannot be empty")
	}

	// Get the quote/cart using the cartToken
	quote, err := getQuoteFromToken(cartToken)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to retrieve quote for cartToken '%s': %v", cartToken, err)
		return nil, fmt.Errorf("failed to retrieve cart: %w", err)
	}

	// Get all items from the quote
	quoteItems, err := quote.GetItems()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to retrieve quote items: %v", err)
		return nil, fmt.Errorf("failed to retrieve cart items: %w", err)
	}

	if len(quoteItems) == 0 {
		log.Printf("[INFO] BNP: No items found in cart, cannot calculate loan")
		return nil, fmt.Errorf("cart is empty")
	}

	// Calculate principal from cart subtotal
	principal := quote.Subtotal.Float64
	if principal <= 0 {
		log.Printf("[WARNING] BNP: Cart subtotal is zero or negative (%.2f)", principal)
		return nil, fmt.Errorf("cart total must be greater than zero")
	}

	// Extract goodTypeIds from cart items (using the same logic as GetCreditCalculatorBNPParibasForQuote)
	var goodTypeIds string
	productRepo := product.NewPraktisProductRepository(nil)

	log.Printf("[INFO] BNP: Analyzing %d cart items to determine good type IDs", len(quoteItems))

	for i, item := range quoteItems {
		if item.Sku == "" {
			log.Printf("[WARNING] BNP: Skipping cart item %d with empty SKU", i+1)
			continue
		}

		log.Printf("[DEBUG] BNP: Processing cart item %d: SKU=%s, Qty=%.2f, Price=%.2f, RowTotal=%.2f",
			i+1, item.Sku, item.Qty, item.Price, item.RowTotal)

		// Fetch product entity to get the good type ID
		productEntity, err := productRepo.GetSKU(item.Sku, []string{
			"stenik_jetcredit_good_type_id",
		})
		if err != nil {
			log.Printf("[WARNING] BNP: Failed to fetch product entity for SKU %s: %v", item.Sku, err)
			continue
		}

		itemGoodTypeIds := productEntity.MustGetVal("stenik_jetcredit_good_type_id")
		if itemGoodTypeIds != "" {
			goodTypeIds = itemGoodTypeIds
			log.Printf("[INFO] BNP: Using good type IDs '%s' from product %s (item %d) for loan calculation", goodTypeIds, item.Sku, i+1)
			break
		} else {
			log.Printf("[DEBUG] BNP: Product %s (item %d) has no good type IDs", item.Sku, i+1)
		}
	}

	// If no good type IDs found, return error
	if goodTypeIds == "" {
		log.Printf("[ERROR] BNP: No products with good type IDs found in cart")
		return nil, fmt.Errorf("no products in cart support BNP financing")
	}

	log.Printf("[INFO] BNP: Calculating loan for principal=%.2f, goodTypeIds='%s', downPayment=%.2f, pricingVariantId=%d",
		principal, goodTypeIds, downPayment, pricingVariantId)

	// Use the legacy function to perform the actual calculation
	return CalculateBNPLoanLegacy(goodTypeIds, principal, downPayment, pricingVariantId)
}

// CalculateBNPLoanLegacy calculates loan details for a specific pricing variant (legacy function for backward compatibility)
func CalculateBNPLoanLegacy(goodTypeIds string, principal float64, downPayment float64, pricingVariantId int) (*model.LoanCalculation, error) {
	if goodTypeIds == "" {
		return nil, fmt.Errorf("goodTypeIds cannot be empty")
	}

	bnpAPI, err := NewBNPCalculatorAPI()
	if err != nil {
		return nil, err
	}

	return bnpAPI.CalculateLoan(goodTypeIds, principal, downPayment, pricingVariantId)
}

// getQuoteFromToken retrieves a quote using the cart token
// This is a helper function that mimics the behavior of request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
func getQuoteFromToken(cartToken string) (*sales.MagentoQuote, error) {
	// Create a new quote repository
	quoteRepo := sales.NewQuoteRepository(nil)

	// Get the quote using the token
	quote, err := quoteRepo.GetQuote(cartToken)
	if err != nil {
		return nil, err
	}

	return quote, nil
}

// GetBNPGoodCategories returns all available good categories from BNP API with caching
func GetBNPGoodCategories() ([]*model.BNPGoodCategory, error) {
	log.Printf("[INFO] BNP: Starting GetBNPGoodCategories request")

	// Check cache first
	goodCategoriesMutex.RLock()
	if goodCategoriesCache != nil && time.Since(goodCategoriesCacheTime) < cacheDuration {
		log.Printf("[INFO] BNP: Returning cached good categories (%d items)", len(goodCategoriesCache))
		defer goodCategoriesMutex.RUnlock()
		return goodCategoriesCache, nil
	}
	goodCategoriesMutex.RUnlock()

	// Cache miss or expired, fetch from API
	goodCategoriesMutex.Lock()
	defer goodCategoriesMutex.Unlock()

	// Double-check in case another goroutine updated the cache
	if goodCategoriesCache != nil && time.Since(goodCategoriesCacheTime) < cacheDuration {
		log.Printf("[INFO] BNP: Returning cached good categories after lock (%d items)", len(goodCategoriesCache))
		return goodCategoriesCache, nil
	}

	bnpAPI, err := NewBNPCalculatorAPI()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to create BNP API client: %v", err)
		return nil, err
	}

	categories, err := bnpAPI.GetGoodCategories()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to get good categories: %v", err)
		return nil, err
	}

	// Update cache
	goodCategoriesCache = categories
	goodCategoriesCacheTime = time.Now()

	log.Printf("[INFO] BNP: Successfully retrieved and cached %d good categories", len(categories))
	return categories, nil
}

// GetBNPGoodTypes returns good types for a specific category, or all types if categoryId is empty with caching
func GetBNPGoodTypes(categoryId *string) ([]*model.BNPGoodType, error) {
	log.Printf("[INFO] BNP: Starting GetBNPGoodTypes request")

	cacheKey := "all"
	if categoryId != nil && *categoryId != "" {
		cacheKey = *categoryId
	}

	// Check cache first
	goodTypesMutex.RLock()
	if cachedTypes, exists := goodTypesCache[cacheKey]; exists {
		if cacheTime, timeExists := goodTypesCacheTime[cacheKey]; timeExists && time.Since(cacheTime) < cacheDuration {
			log.Printf("[INFO] BNP: Returning cached good types for key '%s' (%d items)", cacheKey, len(cachedTypes))
			defer goodTypesMutex.RUnlock()
			return cachedTypes, nil
		}
	}
	goodTypesMutex.RUnlock()

	// Cache miss or expired, fetch from API
	goodTypesMutex.Lock()
	defer goodTypesMutex.Unlock()

	// Double-check in case another goroutine updated the cache
	if cachedTypes, exists := goodTypesCache[cacheKey]; exists {
		if cacheTime, timeExists := goodTypesCacheTime[cacheKey]; timeExists && time.Since(cacheTime) < cacheDuration {
			log.Printf("[INFO] BNP: Returning cached good types after lock for key '%s' (%d items)", cacheKey, len(cachedTypes))
			return cachedTypes, nil
		}
	}

	bnpAPI, err := NewBNPCalculatorAPI()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to create BNP API client: %v", err)
		return nil, err
	}

	var allTypes []*model.BNPGoodType

	if categoryId != nil && *categoryId != "" {
		// Get types for specific category
		log.Printf("[INFO] BNP: Getting good types for category: %s", *categoryId)
		types, err := bnpAPI.GetGoodTypes(*categoryId)
		if err != nil {
			log.Printf("[ERROR] BNP: Failed to get good types for category %s: %v", *categoryId, err)
			return nil, err
		}
		allTypes = types
	} else {
		// Get all categories first, then get types for each category
		log.Printf("[INFO] BNP: Getting good types for all categories")
		categories, err := bnpAPI.GetGoodCategories()
		if err != nil {
			log.Printf("[ERROR] BNP: Failed to get good categories: %v", err)
			return nil, err
		}

		for _, category := range categories {
			// Convert int64 ID back to string for API call
			categoryIDStr := fmt.Sprintf("%d", category.ID)
			types, err := bnpAPI.GetGoodTypes(categoryIDStr)
			if err != nil {
				log.Printf("[WARNING] BNP: Failed to get good types for category %d: %v", category.ID, err)
				continue // Continue with other categories
			}
			allTypes = append(allTypes, types...)
		}
	}

	// Update cache
	goodTypesCache[cacheKey] = allTypes
	goodTypesCacheTime[cacheKey] = time.Now()

	log.Printf("[INFO] BNP: Successfully retrieved and cached %d good types for key '%s'", len(allTypes), cacheKey)
	return allTypes, nil
}

// GetBNPGoodTypesWithIntID is a wrapper function for GraphQL resolver that handles int64 ID conversion
func GetBNPGoodTypesWithIntID(categoryId *int64) ([]*model.BNPGoodType, error) {
	var categoryIdStr *string
	if categoryId != nil {
		str := fmt.Sprintf("%d", *categoryId)
		categoryIdStr = &str
	}
	return GetBNPGoodTypes(categoryIdStr)
}
